package com.webank.maling.ai.vector;

import com.webank.maling.ai.service.AIServiceFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * OpenAI向量生成器（重构版本）
 * 使用新的响应式向量服务，保持向后兼容性
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenAIVectorGenerator {

    private final AIServiceFacade aiServiceFacade;

    @Autowired
    public OpenAIVectorGenerator(AIServiceFacade aiServiceFacade) {
        this.aiServiceFacade = aiServiceFacade;
        log.info("OpenAI向量生成器已初始化（使用响应式服务）");
    }

    /**
     * 生成向量（异步版本，推荐使用）
     *
     * @param text 文本内容
     * @return 向量的Mono
     */
    public Mono<float[]> generateVectorAsync(String text) {
        log.debug("开始异步生成向量，文本长度: {}", text != null ? text.length() : 0);
        return aiServiceFacade.generateVectorAsync(text)
                .doOnSuccess(vector -> log.debug("异步向量生成完成，维度: {}", vector.length))
                .doOnError(error -> log.error("异步向量生成失败", error));
    }

    /**
     * 生成向量（同步版本，兼容性接口）
     *
     * @param text 文本内容
     * @return 向量数组
     */
    public float[] generateVector(String text) {
        log.debug("开始同步生成向量，文本长度: {}", text != null ? text.length() : 0);
        try {
            float[] vector = aiServiceFacade.generateVectorSync(text);
            log.debug("同步向量生成完成，维度: {}", vector.length);
            return vector;
        } catch (Exception e) {
            log.error("同步向量生成失败", e);
            throw new RuntimeException("向量生成失败", e);
        }
    }

    /**
     * 生成向量（CompletableFuture版本，兼容性接口）
     *
     * @param text 文本内容
     * @return 向量的CompletableFuture
     */
    public CompletableFuture<float[]> generateVectorFuture(String text) {
        log.debug("开始Future向量生成，文本长度: {}", text != null ? text.length() : 0);
        return aiServiceFacade.generateVectorFuture(text)
                .whenComplete((vector, throwable) -> {
                    if (throwable != null) {
                        log.error("Future向量生成失败", throwable);
                    } else {
                        log.debug("Future向量生成完成，维度: {}", vector.length);
                    }
                });
    }

    /**
     * 批量生成向量
     *
     * @param texts 文本列表
     * @return 向量列表的CompletableFuture
     */
    public CompletableFuture<List<float[]>> generateBatchVectors(List<String> texts) {
        log.info("开始批量生成向量，文本数量: {}", texts != null ? texts.size() : 0);
        return aiServiceFacade.generateBatchVectors(texts)
                .toFuture()
                .whenComplete((vectors, throwable) -> {
                    if (throwable != null) {
                        log.error("批量向量生成失败", throwable);
                    } else {
                        log.info("批量向量生成完成，生成了 {} 个向量", vectors.size());
                    }
                });
    }

    /**
     * 计算文本相似度
     *
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度分数
     */
    public double calculateSimilarity(String text1, String text2) {
        log.debug("开始计算文本相似度");
        try {
            Double similarity = aiServiceFacade.calculateSimilarity(text1, text2).block();
            log.debug("文本相似度计算完成: {}", similarity);
            return similarity != null ? similarity : 0.0;
        } catch (Exception e) {
            log.error("计算文本相似度失败", e);
            return 0.0;
        }
    }

    /**
     * 异步计算文本相似度
     *
     * @param text1 文本1
     * @param text2 文本2
     * @return 相似度分数的CompletableFuture
     */
    public CompletableFuture<Double> calculateSimilarityAsync(String text1, String text2) {
        log.debug("开始异步计算文本相似度");
        return aiServiceFacade.calculateSimilarity(text1, text2)
                .toFuture()
                .whenComplete((similarity, throwable) -> {
                    if (throwable != null) {
                        log.error("异步计算文本相似度失败", throwable);
                    } else {
                        log.debug("异步文本相似度计算完成: {}", similarity);
                    }
                });
    }

    /**
     * 检查向量服务是否可用
     *
     * @return 服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            return aiServiceFacade.checkServiceHealth()
                    .map(status -> status.isVectorServiceAvailable())
                    .block();
        } catch (Exception e) {
            log.error("检查向量服务可用性时发生错误", e);
            return false;
        }
    }

    /**
     * 获取服务健康状态
     *
     * @return 服务健康状态
     */
    public AIServiceFacade.AIServiceHealthStatus getServiceHealth() {
        try {
            return aiServiceFacade.checkServiceHealth().block();
        } catch (Exception e) {
            log.error("获取向量服务健康状态时发生错误", e);
            return null;
        }
    }

    /**
     * 预热服务
     */
    public void warmupService() {
        log.info("开始预热向量生成服务");
        try {
            aiServiceFacade.warmupServices().block();
            log.info("向量生成服务预热完成");
        } catch (Exception e) {
            log.warn("向量生成服务预热失败", e);
        }
    }

    // ==================== 静态工厂方法（兼容性支持） ====================

    /**
     * 获取实例（兼容性方法）
     * 注意：在Spring环境中应该使用依赖注入
     */
    @Deprecated
    public static OpenAIVectorGenerator getInstance() {
        log.warn("使用了已废弃的getInstance()方法，建议使用Spring依赖注入");
        throw new UnsupportedOperationException("请使用Spring依赖注入获取OpenAIVectorGenerator实例");
    }

    /**
     * 流式生成向量
     *
     * @param texts 文本列表
     * @return 向量结果流的CompletableFuture
     */
    public CompletableFuture<List<ReactiveVectorService.VectorResult>> generateVectorStream(List<String> texts) {
        log.info("开始流式生成向量，文本数量: {}", texts != null ? texts.size() : 0);
        return aiServiceFacade.generateVectorStream(texts)
                .collectList()
                .toFuture()
                .whenComplete((results, throwable) -> {
                    if (throwable != null) {
                        log.error("流式向量生成失败", throwable);
                    } else {
                        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
                        log.info("流式向量生成完成，成功: {}, 总数: {}", successCount, results.size());
                    }
                });
    }

    /**
     * 批量处理文本并生成向量（带错误处理）
     *
     * @param texts 文本列表
     * @param batchSize 批处理大小
     * @return 处理结果的CompletableFuture
     */
    public CompletableFuture<VectorBatchResult> processBatchWithErrorHandling(List<String> texts, int batchSize) {
        log.info("开始批量处理文本生成向量，文本数量: {}, 批大小: {}", texts.size(), batchSize);
        
        return aiServiceFacade.generateVectorStream(texts)
                .collectList()
                .map(results -> {
                    List<float[]> successVectors = results.stream()
                            .filter(ReactiveVectorService.VectorResult::isSuccess)
                            .map(ReactiveVectorService.VectorResult::getVector)
                            .toList();
                    
                    List<String> failedTexts = results.stream()
                            .filter(r -> !r.isSuccess())
                            .map(ReactiveVectorService.VectorResult::getText)
                            .toList();
                    
                    return new VectorBatchResult(successVectors, failedTexts, results.size());
                })
                .toFuture()
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("批量向量处理失败", throwable);
                    } else {
                        log.info("批量向量处理完成，成功: {}, 失败: {}, 总数: {}", 
                                result.getSuccessVectors().size(), 
                                result.getFailedTexts().size(), 
                                result.getTotalCount());
                    }
                });
    }

    /**
     * 向量批处理结果
     */
    public static class VectorBatchResult {
        private final List<float[]> successVectors;
        private final List<String> failedTexts;
        private final int totalCount;

        public VectorBatchResult(List<float[]> successVectors, List<String> failedTexts, int totalCount) {
            this.successVectors = successVectors;
            this.failedTexts = failedTexts;
            this.totalCount = totalCount;
        }

        public List<float[]> getSuccessVectors() { return successVectors; }
        public List<String> getFailedTexts() { return failedTexts; }
        public int getTotalCount() { return totalCount; }
        public int getSuccessCount() { return successVectors.size(); }
        public int getFailureCount() { return failedTexts.size(); }
        public double getSuccessRate() { return totalCount > 0 ? (double) getSuccessCount() / totalCount : 0.0; }
    }
}
