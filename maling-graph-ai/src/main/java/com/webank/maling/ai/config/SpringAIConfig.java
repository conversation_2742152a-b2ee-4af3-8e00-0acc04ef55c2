package com.webank.maling.ai.config;

import com.webank.maling.base.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.OpenAiEmbeddingApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Spring AI 配置类
 * 配置OpenAI聊天模型和嵌入模型
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SpringAIConfig {
    
    private final AppConfig appConfig;
    
    public SpringAIConfig() {
        this.appConfig = AppConfig.getInstance();
    }
    
    /**
     * 配置WebClient用于响应式HTTP调用
     */
    @Bean
    @Primary
    public WebClient webClient() {
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }
    
    /**
     * 配置OpenAI API客户端用于聊天
     */
    @Bean
    public OpenAiApi openAiChatApi() {
        return new OpenAiApi(appConfig.getChatUrl(), appConfig.getChatApiKey(), webClient());
    }
    
    /**
     * 配置OpenAI聊天模型
     */
    @Bean
    @Primary
    public OpenAiChatModel openAiChatModel() {
        var options = OpenAiChatModel.OpenAiChatOptions.builder()
                .withModel(appConfig.getChatModel())
                .withMaxTokens(4000)
                .withTemperature(0.7f)
                .build();
        
        return new OpenAiChatModel(openAiChatApi(), options);
    }
    
    /**
     * 配置OpenAI嵌入API客户端
     */
    @Bean
    public OpenAiEmbeddingApi openAiEmbeddingApi() {
        return new OpenAiEmbeddingApi(appConfig.getEmbeddingUrl(), appConfig.getEmbeddingApiKey(), webClient());
    }
    
    /**
     * 配置OpenAI嵌入模型
     */
    @Bean
    @Primary
    public EmbeddingModel embeddingModel() {
        var options = OpenAiEmbeddingModel.OpenAiEmbeddingOptions.builder()
                .withModel(appConfig.getEmbeddingModel())
                .build();
        
        return new OpenAiEmbeddingModel(openAiEmbeddingApi(), options);
    }
    
    /**
     * 配置AI服务属性
     */
    @Bean
    public AIServiceProperties aiServiceProperties() {
        return AIServiceProperties.builder()
                .chatModel(appConfig.getChatModel())
                .embeddingModel(appConfig.getEmbeddingModel())
                .chatUrl(appConfig.getChatUrl())
                .embeddingUrl(appConfig.getEmbeddingUrl())
                .maxTokens(4000)
                .temperature(0.7f)
                .maxRetryAttempts(3)
                .retryDelayMs(1000L)
                .build();
    }
    
    /**
     * AI服务配置属性
     */
    public static class AIServiceProperties {
        private String chatModel;
        private String embeddingModel;
        private String chatUrl;
        private String embeddingUrl;
        private Integer maxTokens;
        private Float temperature;
        private Integer maxRetryAttempts;
        private Long retryDelayMs;
        
        public static AIServicePropertiesBuilder builder() {
            return new AIServicePropertiesBuilder();
        }
        
        public static class AIServicePropertiesBuilder {
            private String chatModel;
            private String embeddingModel;
            private String chatUrl;
            private String embeddingUrl;
            private Integer maxTokens;
            private Float temperature;
            private Integer maxRetryAttempts;
            private Long retryDelayMs;
            
            public AIServicePropertiesBuilder chatModel(String chatModel) {
                this.chatModel = chatModel;
                return this;
            }
            
            public AIServicePropertiesBuilder embeddingModel(String embeddingModel) {
                this.embeddingModel = embeddingModel;
                return this;
            }
            
            public AIServicePropertiesBuilder chatUrl(String chatUrl) {
                this.chatUrl = chatUrl;
                return this;
            }
            
            public AIServicePropertiesBuilder embeddingUrl(String embeddingUrl) {
                this.embeddingUrl = embeddingUrl;
                return this;
            }
            
            public AIServicePropertiesBuilder maxTokens(Integer maxTokens) {
                this.maxTokens = maxTokens;
                return this;
            }
            
            public AIServicePropertiesBuilder temperature(Float temperature) {
                this.temperature = temperature;
                return this;
            }
            
            public AIServicePropertiesBuilder maxRetryAttempts(Integer maxRetryAttempts) {
                this.maxRetryAttempts = maxRetryAttempts;
                return this;
            }
            
            public AIServicePropertiesBuilder retryDelayMs(Long retryDelayMs) {
                this.retryDelayMs = retryDelayMs;
                return this;
            }
            
            public AIServiceProperties build() {
                AIServiceProperties properties = new AIServiceProperties();
                properties.chatModel = this.chatModel;
                properties.embeddingModel = this.embeddingModel;
                properties.chatUrl = this.chatUrl;
                properties.embeddingUrl = this.embeddingUrl;
                properties.maxTokens = this.maxTokens;
                properties.temperature = this.temperature;
                properties.maxRetryAttempts = this.maxRetryAttempts;
                properties.retryDelayMs = this.retryDelayMs;
                return properties;
            }
        }
        
        // Getters
        public String getChatModel() { return chatModel; }
        public String getEmbeddingModel() { return embeddingModel; }
        public String getChatUrl() { return chatUrl; }
        public String getEmbeddingUrl() { return embeddingUrl; }
        public Integer getMaxTokens() { return maxTokens; }
        public Float getTemperature() { return temperature; }
        public Integer getMaxRetryAttempts() { return maxRetryAttempts; }
        public Long getRetryDelayMs() { return retryDelayMs; }
    }
}
