package com.webank.maling.ai.documentation;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.webank.maling.base.config.AppConfig;
import com.webank.maling.base.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * AI文档生成服务 - 单例模式
 * 负责调用AI大模型生成说明书内容
 *
 * <AUTHOR>
 */
@Slf4j
public class AIDocumentationService implements AutoCloseable {

    // 单例实例
    private static volatile AIDocumentationService instance;
    private static final Object lock = new Object();

    private final AppConfig config;
    private final CloseableHttpClient httpClient;
    private final AIPromptBuilder promptBuilder;
    private final BatchDocumentationService batchDocumentationService;

    // 重试配置
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;

    /**
     * 私有构造函数，防止外部实例化
     */
    private AIDocumentationService() {
        this.config = AppConfig.getInstance();
        this.httpClient = HttpClients.createDefault();
        this.promptBuilder = new AIPromptBuilder();
        this.batchDocumentationService = new BatchDocumentationService(promptBuilder, this);
        log.info("AI文档生成服务单例已初始化");
    }

    /**
     * 获取单例实例 - 双重检查锁定模式
     *
     * @return AIDocumentationService单例实例
     */
    public static AIDocumentationService getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new AIDocumentationService();
                }
            }
        }
        return instance;
    }

    /**
     * 为指定层级生成文档内容
     *
     * @param context 文档生成上下文
     * @return 生成的文档内容
     */
    public CompletableFuture<String> generateDocumentationAsync(DocumentationGenerationContext context) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateDocumentationForLevel(context);
            } catch (Exception e) {
                log.error("异步生成文档时发生错误", e);
                throw new RuntimeException("文档生成失败", e);
            }
        });
    }

    /**
     * 同步生成文档内容
     *
     * @param context 文档生成上下文
     * @return 生成的文档内容
     */
    public String generateDocumentationForLevel(DocumentationGenerationContext context) {
        try {
            log.info("开始为层级 {} 生成文档，入口点: {}", context.getLevel(), context.getEntryPointId());

            // 检查是否需要分批处理（所有层级都支持）
            if (batchDocumentationService.needsBatchProcessing(context)) {
                log.info("第{}层内容过大，使用分批处理模式", context.getLevel());
                String batchContent = batchDocumentationService.generateBatchDocumentation(context);

                if (batchContent == null || batchContent.trim().isEmpty()) {
                    log.error("分批生成文档失败");
                    return null;
                }

                // 后处理分批生成的内容
                String processedContent = postProcessContent(batchContent, context);
                log.info("成功完成第{}层分批文档生成，内容长度: {} 字符", context.getLevel(), processedContent.length());

                return processedContent;
            }

            // 常规单次生成流程
            // 1. 构建提示词
            String prompt = promptBuilder.buildPromptForLevel(context);

            if (prompt == null || prompt.trim().isEmpty()) {
                log.error("构建提示词失败");
                return null;
            }

            // 2. 调用AI服务生成内容
            String content = callAIServiceWithRetry(prompt);

            if (content == null || content.trim().isEmpty()) {
                log.error("AI服务返回空内容");
                return null;
            }

            // 3. 后处理生成的内容
            String processedContent = postProcessContent(content, context);

            log.info("成功生成层级 {} 的文档，内容长度: {} 字符", context.getLevel(), processedContent.length());

            return processedContent;

        } catch (Exception e) {
            log.error("生成文档时发生错误", e);
            return null;
        }
    }

    /**
     * 带重试机制的AI服务调用
     */
    private String callAIServiceWithRetry(String prompt) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                log.debug("第 {} 次尝试调用AI服务", attempt);

                String result = callAIService(prompt);

                if (result != null && !result.trim().isEmpty()) {
                    log.debug("AI服务调用成功，第 {} 次尝试", attempt);
                    return result;
                }

                log.warn("AI服务返回空内容，第 {} 次尝试", attempt);

            } catch (Exception e) {
                lastException = e;
                log.warn("AI服务调用失败，第 {} 次尝试: {}", attempt, e.getMessage());

                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("AI服务调用失败，已重试 {} 次", MAX_RETRY_ATTEMPTS, lastException);
        return null;
    }

    /**
     * 调用AI服务（公共方法，供BatchDocumentationService使用）
     */
    public String callAIService(String prompt) throws IOException {
        HttpPost httpPost = createHttpPost(prompt);

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            int statusCode = response.getStatusLine().getStatusCode();
            HttpEntity responseEntity = response.getEntity();
            String responseBody = responseEntity != null ? EntityUtils.toString(responseEntity) : null;

            log.debug("AI服务响应状态码: {}", statusCode);

            if (statusCode == 200) {
                return parseAIResponse(responseBody);
            } else {
                log.error("AI服务调用失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new IOException("AI服务返回错误状态码: " + statusCode);
            }
        }
    }

    /**
     * 创建HTTP请求
     */
    private HttpPost createHttpPost(String prompt) {
        HttpPost httpPost = new HttpPost(config.getChatUrl());

        // 构建请求体
        JsonObject requestObject = new JsonObject();
        requestObject.addProperty("model", config.getChatModel());

        // 构建消息数组
        JsonObject messageObject = new JsonObject();
        messageObject.addProperty("role", "user");
        messageObject.addProperty("content", prompt);

        JsonArray messagesArray = new JsonArray();
        messagesArray.add(messageObject);

        requestObject.add("messages", messagesArray);
        requestObject.addProperty("max_tokens", 4000);
        requestObject.addProperty("temperature", 0.7);

        String requestBody = Json.toJson(requestObject);

        StringEntity requestEntity = new StringEntity(requestBody, ContentType.APPLICATION_JSON);
        httpPost.setEntity(requestEntity);

        // 设置请求头
        httpPost.setHeader("Authorization", "Bearer " + config.getChatApiKey());
        httpPost.setHeader("Content-Type", "application/json");

        return httpPost;
    }

    /**
     * 解析AI服务响应
     */
    private String parseAIResponse(String responseBody) throws IOException {
        try {
            JsonObject jsonObject = Json.fromJson(responseBody, JsonObject.class);

            // 解析OpenAI格式的响应
            if (jsonObject.has("choices")) {
                JsonArray choicesArray = jsonObject.getAsJsonArray("choices");
                if (choicesArray.size() > 0) {
                    JsonObject firstChoice = choicesArray.get(0).getAsJsonObject();
                    if (firstChoice.has("message")) {
                        JsonObject messageObject = firstChoice.getAsJsonObject("message");
                        if (messageObject.has("content")) {
                            return messageObject.get("content").getAsString();
                        }
                    }
                }
            }

            log.error("无法从AI响应中解析内容: {}", responseBody);
            return null;

        } catch (Exception e) {
            log.error("解析AI响应时发生错误: {}", responseBody, e);
            throw new IOException("解析AI响应失败", e);
        }
    }

    /**
     * 后处理生成的内容
     */
    private String postProcessContent(String content, DocumentationGenerationContext context) {
        if (content == null) {
            return null;
        }

        // 1. 清理多余的空白字符
        String processed = content.trim().replaceAll("\\n{3,}", "\n\n");

//        // 2. 检查内容长度限制
//        int maxLength = getMaxLengthForLevel(context.getLevel());
//        if (processed.length() > maxLength) {
//            log.warn("生成的内容超过长度限制 {} 字符，将进行截断", maxLength);
//            processed = processed.substring(0, maxLength - 3) + "...";
//        }

        // 3. 添加生成信息
        String footer = String.format("\n\n---\n*本说明书由AI自动生成，层级: %d，生成时间: %s*",
                context.getLevel(), java.time.LocalDateTime.now().toString());

        return processed + footer;
    }

    /**
     * 根据层级获取最大长度限制
     */
    private int getMaxLengthForLevel(int level) {
        return switch (level) {
            case 1 -> 1000;
            case 2 -> 3000;
            case 3 -> 10000;
            default -> 10000;
        };
    }

    /**
     * 检查AI服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            String testPrompt = "请回复'服务正常'";
            String response = callAIService(testPrompt);
            return response != null && !response.trim().isEmpty();
        } catch (Exception e) {
            log.warn("AI服务不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取服务状态信息
     */
    public AIServiceStatus getServiceStatus() {
        try {
            boolean available = isServiceAvailable();
            return AIServiceStatus.builder()
                    .available(available)
                    .model(config.getChatModel())
                    .endpoint(config.getChatUrl())
                    .lastCheckTime(java.time.LocalDateTime.now())
                    .build();
        } catch (Exception e) {
            return AIServiceStatus.builder()
                    .available(false)
                    .error(e.getMessage())
                    .lastCheckTime(java.time.LocalDateTime.now())
                    .build();
        }
    }

    /**
     * 关闭资源但不销毁单例
     */
    @Override
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
            log.info("AI文档生成服务资源已关闭");
        } catch (IOException e) {
            log.error("关闭AI文档生成服务资源时发生错误", e);
        }
    }

    /**
     * 销毁单例实例（仅在应用程序关闭时调用）
     */
    public static void destroyInstance() {
        if (instance != null) {
            synchronized (lock) {
                if (instance != null) {
                    try {
                        instance.close();
                    } catch (Exception e) {
                        log.error("销毁单例实例时发生错误", e);
                    } finally {
                        instance = null;
                        log.info("AI文档生成服务单例已销毁");
                    }
                }
            }
        }
    }

    /**
     * AI服务状态信息
     */
    public static class AIServiceStatus {
        private boolean available;
        private String model;
        private String endpoint;
        private String error;
        private java.time.LocalDateTime lastCheckTime;

        public static AIServiceStatusBuilder builder() {
            return new AIServiceStatusBuilder();
        }

        // Builder implementation
        public static class AIServiceStatusBuilder {
            private boolean available;
            private String model;
            private String endpoint;
            private String error;
            private java.time.LocalDateTime lastCheckTime;

            public AIServiceStatusBuilder available(boolean available) {
                this.available = available;
                return this;
            }

            public AIServiceStatusBuilder model(String model) {
                this.model = model;
                return this;
            }

            public AIServiceStatusBuilder endpoint(String endpoint) {
                this.endpoint = endpoint;
                return this;
            }

            public AIServiceStatusBuilder error(String error) {
                this.error = error;
                return this;
            }

            public AIServiceStatusBuilder lastCheckTime(java.time.LocalDateTime lastCheckTime) {
                this.lastCheckTime = lastCheckTime;
                return this;
            }

            public AIServiceStatus build() {
                AIServiceStatus status = new AIServiceStatus();
                status.available = this.available;
                status.model = this.model;
                status.endpoint = this.endpoint;
                status.error = this.error;
                status.lastCheckTime = this.lastCheckTime;
                return status;
            }
        }

        // Getters
        public boolean isAvailable() { return available; }
        public String getModel() { return model; }
        public String getEndpoint() { return endpoint; }
        public String getError() { return error; }
        public java.time.LocalDateTime getLastCheckTime() { return lastCheckTime; }
    }
}
