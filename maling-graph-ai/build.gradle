description = 'Maling Graph Vector - 向量服务'

dependencies {
    // 依赖核心模块
    api project(':maling-graph-base')

    // Spring AI - OpenAI integration
    implementation 'org.springframework.ai:spring-ai-openai-spring-boot-starter:1.0.0-M3'
    implementation 'org.springframework.ai:spring-ai-core:1.0.0-M3'

    // Spring Boot WebFlux for reactive support
    implementation 'org.springframework.boot:spring-boot-starter-webflux:3.2.0'

    // OpenAI client for vector generation (keep for compatibility)
    api "com.theokanning.openai-gpt3-java:service:${openaiVersion}"

    // HTTP client (keep for legacy support)
    implementation "org.apache.httpcomponents:httpclient:${httpclientVersion}"
    implementation "org.apache.httpcomponents:httpcore:${httpcoreVersion}"
    
    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
    
    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"
    
    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}
