package com.webank.maling.documentation.service;

import com.webank.maling.base.entity.Documentation;
import com.webank.maling.base.entity.DocumentationTask;
import com.webank.maling.base.model.MethodInfo;
import com.webank.maling.documentation.dto.DocumentationGenerationDto;
import com.webank.maling.documentation.repository.graph.EntryPointRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 文档生成任务执行服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class DocumentationJobService {
    
    @Autowired
    private DocumentationTaskService taskService;
    
    @Autowired
    private ProgressiveDocumentationService progressiveService;
    
    @Autowired
    private EntryPointRepository entryPointRepository;
    
    /**
     * 执行文档生成任务
     * 
     * @param task 要执行的任务
     * @return 是否执行成功
     */
    public boolean executeDocumentationTask(DocumentationTask task) {
        if (task == null) {
            log.error("任务为空，无法执行");
            return false;
        }
        
        Long taskId = task.getId();
        String entryPointId = task.getEntryPointId();
        
        try {
            log.info("开始执行文档生成任务: {}, 入口点: {}, 目标层级: {}", 
                    taskId, entryPointId, task.getTargetLevel());
            
            // 更新任务状态为运行中
            taskService.updateTaskStatus(taskId, DocumentationTask.TaskStatus.RUNNING);
            
            // 检查入口点是否存在
            MethodInfo entryPoint = entryPointRepository.getEntryPointById(entryPointId);
            if (entryPoint == null) {
                String errorMsg = "入口点不存在: " + entryPointId;
                log.error(errorMsg);
                taskService.updateTaskStatus(taskId, DocumentationTask.TaskStatus.FAILED, errorMsg);
                return false;
            }
            
            // 构建生成请求
            DocumentationGenerationDto dto = DocumentationGenerationDto.builder()
                    .projectId(task.getProjectId())
                    .branchName(task.getBranchName())
                    .level(task.getTargetLevel())
                    .forceRegenerate(true) // 任务执行时强制重新生成
                    .description("定时任务执行")
                    .build();
            
            // 执行渐进式生成
            Documentation result = progressiveService.executeProgressiveGeneration(task, dto, entryPoint);
            
            if (result != null) {
                // 任务执行成功
                taskService.updateTaskStatus(taskId, DocumentationTask.TaskStatus.COMPLETED);
                log.info("任务 {} 执行成功，生成文档ID: {}", taskId, result.getId());
                return true;
            } else {
                // 任务执行失败
                String errorMsg = "文档生成失败，返回结果为空";
                log.error("任务 {} 执行失败: {}", taskId, errorMsg);
                taskService.updateTaskStatus(taskId, DocumentationTask.TaskStatus.FAILED, errorMsg);
                return false;
            }
            
        } catch (Exception e) {
            log.error("执行任务 {} 时发生异常", taskId, e);
            
            try {
                // 更新任务失败状态
                String errorMsg = "执行异常: " + e.getMessage();
                taskService.updateTaskStatus(taskId, DocumentationTask.TaskStatus.FAILED, errorMsg);
            } catch (Exception ex) {
                log.error("更新任务失败状态时发生异常", ex);
            }
            
            return false;
        }
    }
    
    /**
     * 处理任务失败，包括重试逻辑
     * 
     * @param task 失败的任务
     * @param errorMessage 错误信息
     */
    public void handleTaskFailure(DocumentationTask task, String errorMessage) {
        if (task == null) {
            return;
        }
        
        Long taskId = task.getId();
        Integer currentRetryCount = task.getRetryCount() != null ? task.getRetryCount() : 0;
        Integer maxRetryCount = task.getMaxRetryCount() != null ? task.getMaxRetryCount() : 3;
        
        log.info("处理任务失败: {}, 当前重试次数: {}, 最大重试次数: {}", 
                taskId, currentRetryCount, maxRetryCount);
        
        if (currentRetryCount < maxRetryCount) {
            // 可以重试，计算下次执行时间
            int newRetryCount = currentRetryCount + 1;
            LocalDateTime nextExecuteTime = calculateNextExecuteTime(newRetryCount);
            
            log.info("任务 {} 将进行第 {} 次重试，下次执行时间: {}", 
                    taskId, newRetryCount, nextExecuteTime);
            
            // 更新重试信息，状态改为等待
            taskService.updateRetryInfo(taskId, newRetryCount, nextExecuteTime, 
                    DocumentationTask.TaskStatus.WAITING, errorMessage);
        } else {
            // 超过最大重试次数，标记为最终失败
            log.warn("任务 {} 超过最大重试次数 {}，标记为最终失败", taskId, maxRetryCount);
            taskService.updateTaskStatus(taskId, DocumentationTask.TaskStatus.FAILED, 
                    "超过最大重试次数: " + errorMessage);
        }
    }
    
    /**
     * 计算下次执行时间
     * 使用指数退避策略
     * 
     * @param retryCount 重试次数
     * @return 下次执行时间
     */
    private LocalDateTime calculateNextExecuteTime(int retryCount) {
        // 指数退避：1分钟、2分钟、4分钟、8分钟...
        long delayMinutes = (long) Math.pow(2, retryCount - 1);
        
        // 最大延迟不超过60分钟
        delayMinutes = Math.min(delayMinutes, 60);
        
        return LocalDateTime.now().plusMinutes(delayMinutes);
    }
    
    /**
     * 检查任务是否可以执行
     * 
     * @param task 任务
     * @return 是否可以执行
     */
    public boolean canExecuteTask(DocumentationTask task) {
        if (task == null) {
            return false;
        }
        
        // 检查状态
        if (task.getStatus() != DocumentationTask.TaskStatus.WAITING) {
            return false;
        }
        
        // 检查重试次数
        Integer retryCount = task.getRetryCount() != null ? task.getRetryCount() : 0;
        Integer maxRetryCount = task.getMaxRetryCount() != null ? task.getMaxRetryCount() : 3;
        
        if (retryCount >= maxRetryCount) {
            return false;
        }
        
        // 检查执行时间
        LocalDateTime nextExecuteTime = task.getNextExecuteTime();
        if (nextExecuteTime != null && LocalDateTime.now().isBefore(nextExecuteTime)) {
            return false;
        }
        
        return true;
    }
}
